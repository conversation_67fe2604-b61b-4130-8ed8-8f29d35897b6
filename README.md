# ME3 Mod Manager

基于PySide6+QML的现代化ME3 mod管理器，支持自动工具下载、游戏配置和mod管理。

## 功能特性

### ✨ 现代化UI设计
- 无边框窗口设计
- 自定义标题栏，支持窗口拖动
- 明暗主题切换
- 中英文国际化支持
- 响应式布局设计

### 🔧 工具管理
- 自动从GitHub下载ME3最新版本
- 支持多个加速代理（gh-proxy.com、ghproxy.net、ghfast.top）
- 自动解压和版本管理
- 下载进度显示

### ⚙️ 游戏配置
- 游戏路径配置和验证
- 破解文件管理（OnlineFix）
- 一键应用/移除破解
- 配置文件自动保存

### 📦 Mod配置
- ME3配置文件(.me3)的可视化编辑
- 支持多个配置文件管理
- 实时配置预览
- TOML格式验证

### 🚀 游戏启动
- 参数化启动ME3
- 配置文件选择
- 启动命令预览
- 支持跳过Steam初始化

## 安装和使用

### 环境要求
- Python 3.8+
- PySide6 6.6.0+

### 安装依赖
```bash
pip install -r requirements.txt
```

### 运行应用
```bash
python run.py
```

## 项目结构

```
ME3ModManager/
├── src/                    # 源代码目录
│   ├── core/              # 核心模块
│   │   ├── app_manager.py # 应用程序管理器
│   │   ├── config_manager.py # 配置管理器
│   │   └── theme_manager.py # 主题管理器
│   ├── models/            # 数据模型
│   │   ├── tool_manager.py # 工具管理器
│   │   ├── game_manager.py # 游戏管理器
│   │   └── mod_manager.py  # Mod管理器
│   └── ui/                # 用户界面
│       ├── qml/           # QML界面文件
│       └── components/    # UI组件
├── TOOLS/                 # 工具存储目录
│   └── OnlineFix/        # 破解文件目录
├── ME3/                   # ME3工具目录
├── Mods/                  # Mod配置文件目录
├── resources/             # 资源文件
├── main.py               # 主入口文件
├── run.py                # 快速启动脚本
└── requirements.txt      # 依赖列表
```

## 使用说明

### 1. 工具管理
1. 打开"工具管理"页面
2. 点击"下载最新版本"按钮
3. 等待下载和安装完成
4. 如遇网络问题，可点击"切换代理"

### 2. 基础配置
1. 打开"基础配置"页面
2. 点击"选择游戏文件"，选择nightreign.exe
3. 点击"应用破解"来安装破解文件
4. 或点击"移除破解"来卸载破解文件

### 3. Mod配置
1. 打开"Mod配置"页面
2. 点击"新建配置"创建新的配置文件
3. 在编辑器中编辑TOML格式的配置
4. 点击"保存"保存配置

### 4. 启动游戏
1. 打开"启动游戏"页面
2. 确认游戏路径和配置文件
3. 选择是否跳过Steam初始化
4. 点击"启动游戏"

## 配置文件格式

ME3配置文件使用TOML格式，示例：

```toml
profileVersion = "v1"

[[packages]]
id = "my-texture-pack"
path = "mods/MyTexturePack/"

[[packages]]
id = "my-model-pack"
path = "mods/MyModelPack/"

[[natives]]
path = "mods/MyAwesomeMod.dll"
```

## 开发说明

### 技术栈
- **后端**: Python + PySide6
- **前端**: QML + Material Design
- **配置**: TOML格式
- **网络**: requests库
- **文件处理**: pathlib + zipfile

### 模块化设计
- **核心层**: 应用程序生命周期管理
- **模型层**: 业务逻辑和数据管理
- **视图层**: QML用户界面

### 扩展开发
1. 在`src/models/`中添加新的管理器
2. 在`src/ui/qml/`中添加对应的QML页面
3. 在`main.py`中注册新的QML类型

## 许可证

本项目基于MIT许可证开源。

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 致谢

- [ME3项目](https://github.com/garyttierney/me3) - 提供核心mod加载功能
- PySide6团队 - 提供优秀的Python Qt绑定
- Material Design - 提供现代化的设计语言
