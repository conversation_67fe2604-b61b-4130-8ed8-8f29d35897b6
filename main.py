#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ME3 Mod Manager - 主入口文件
基于PySide6+QML的现代化mod管理器
"""

import sys
import os
from pathlib import Path
from PySide6.QtGui import QGuiApplication, QIcon
from PySide6.QtQml import qmlRegisterType
from PySide6.QtQuickControls2 import QQuickStyle

from src.core.app_manager import AppManager
from src.models.tool_manager import ToolManager
from src.models.mod_manager import ModManager
from src.models.game_manager import GameManager
from src.models.launch_manager import LaunchManager


def main():
    """主函数"""
    # 设置应用程序属性
    QGuiApplication.setApplicationName("ME3 Mod Manager")
    QGuiApplication.setApplicationVersion("1.0.0")
    QGuiApplication.setOrganizationName("ME3ModManager")
    QGuiApplication.setOrganizationDomain("me3modmanager.local")
    
    # 创建应用程序实例
    app = QGuiApplication(sys.argv)
    
    # 设置应用程序图标
    app_icon = QIcon("resources/icons/app_icon.png")
    app.setWindowIcon(app_icon)
    
    # 设置QML样式
    QQuickStyle.setStyle("Material")
    
    # 注册QML类型
    qmlRegisterType(ToolManager, "ME3ModManager", 1, 0, "ToolManager")
    qmlRegisterType(ModManager, "ME3ModManager", 1, 0, "ModManager")
    qmlRegisterType(GameManager, "ME3ModManager", 1, 0, "GameManager")
    qmlRegisterType(LaunchManager, "ME3ModManager", 1, 0, "LaunchManager")
    
    # 创建应用程序管理器
    app_manager = AppManager()
    
    # 启动应用程序
    if app_manager.initialize():
        return app.exec()
    else:
        print("应用程序初始化失败")
        return 1


if __name__ == "__main__":
    sys.exit(main())
