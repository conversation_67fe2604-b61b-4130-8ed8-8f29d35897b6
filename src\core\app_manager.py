#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
应用程序管理器 - 负责整个应用程序的初始化和生命周期管理
"""

import os
import sys
from pathlib import Path
from PySide6.QtCore import QObject, QUrl, QTranslator, QLocale, QCoreApplication
from PySide6.QtQml import QQmlApplicationEngine
from PySide6.QtGui import QGuiApplication

from src.core.config_manager import ConfigManager
from src.core.theme_manager import ThemeManager
from src.models.tool_manager import ToolManager
from src.models.mod_manager import ModManager
from src.models.game_manager import GameManager
from src.models.launch_manager import LaunchManager


class AppManager(QObject):
    """应用程序管理器"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.engine = None
        self.config_manager = None
        self.theme_manager = None
        self.tool_manager = None
        self.mod_manager = None
        self.game_manager = None
        self.launch_manager = None
        self.translator = None
        
    def initialize(self) -> bool:
        """初始化应用程序"""
        try:
            # 初始化配置管理器
            self.config_manager = ConfigManager()
            
            # 初始化主题管理器
            self.theme_manager = ThemeManager()
            
            # 初始化各个功能模块
            self.tool_manager = ToolManager()
            self.mod_manager = ModManager()
            self.game_manager = GameManager()
            self.launch_manager = LaunchManager()
            
            # 设置语言
            self._setup_language()
            
            # 创建QML引擎
            self.engine = QQmlApplicationEngine()
            
            # 注册上下文属性
            self._register_context_properties()
            
            # 加载主QML文件
            qml_file = Path(__file__).resolve().parent.parent / "ui" / "qml" / "main.qml"
            self.engine.load(QUrl.fromLocalFile(str(qml_file)))
            
            # 检查是否成功加载
            if not self.engine.rootObjects():
                print("Failed to load QML file")
                return False
                
            return True
            
        except Exception as e:
            print(f"应用程序初始化失败: {e}")
            return False
    
    def _setup_language(self):
        """设置应用程序语言"""
        try:
            # 获取配置的语言设置
            language = self.config_manager.get_language()
            
            # 创建翻译器
            self.translator = QTranslator()
            
            # 加载翻译文件
            translation_file = f"resources/translations/app_{language}.qm"
            if os.path.exists(translation_file):
                self.translator.load(translation_file)
                QCoreApplication.installTranslator(self.translator)
                
        except Exception as e:
            print(f"语言设置失败: {e}")
    
    def _register_context_properties(self):
        """注册QML上下文属性"""
        if self.engine:
            # 注册管理器实例
            self.engine.rootContext().setContextProperty("appManager", self)
            self.engine.rootContext().setContextProperty("configManager", self.config_manager)
            self.engine.rootContext().setContextProperty("themeManager", self.theme_manager)
            self.engine.rootContext().setContextProperty("toolManager", self.tool_manager)
            self.engine.rootContext().setContextProperty("modManager", self.mod_manager)
            self.engine.rootContext().setContextProperty("gameManager", self.game_manager)
            self.engine.rootContext().setContextProperty("launchManager", self.launch_manager)
    
    def quit_application(self):
        """退出应用程序"""
        QGuiApplication.quit()
