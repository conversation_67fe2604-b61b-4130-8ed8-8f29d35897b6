#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器 - 负责应用程序配置的读取、保存和管理
"""

import os
import json
from pathlib import Path
from PySide6.QtCore import QObject, Signal, Slot, QStandardPaths


class ConfigManager(QObject):
    """配置管理器"""
    
    # 信号
    config_changed = Signal(str, object)  # 配置项名称, 新值
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.config_file = self._get_config_file_path()
        self.config_data = {}
        self.load_config()
    
    def _get_config_file_path(self) -> Path:
        """获取配置文件路径"""
        # 使用用户数据目录
        app_data_dir = QStandardPaths.writableLocation(QStandardPaths.AppDataLocation)
        config_dir = Path(app_data_dir)
        config_dir.mkdir(parents=True, exist_ok=True)
        return config_dir / "config.json"
    
    def load_config(self):
        """加载配置"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config_data = json.load(f)
            else:
                # 创建默认配置
                self.config_data = self._get_default_config()
                self.save_config()
        except Exception as e:
            print(f"加载配置失败: {e}")
            self.config_data = self._get_default_config()
    
    def save_config(self):
        """保存配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存配置失败: {e}")
    
    def _get_default_config(self) -> dict:
        """获取默认配置"""
        return {
            "language": "zh_CN",
            "theme": "dark",
            "window": {
                "width": 1200,
                "height": 800,
                "x": 100,
                "y": 100
            },
            "game": {
                "path": "",
                "last_profile": ""
            },
            "tools": {
                "me3_version": "",
                "auto_update": True,
                "proxy_index": 0  # 0: gh-proxy.com, 1: ghproxy.net, 2: ghfast.top
            }
        }
    
    def get(self, key: str, default=None):
        """获取配置值"""
        keys = key.split('.')
        value = self.config_data
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value):
        """设置配置值"""
        keys = key.split('.')
        config = self.config_data
        
        # 导航到目标位置
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # 设置值
        config[keys[-1]] = value
        
        # 保存配置
        self.save_config()
        
        # 发出信号
        self.config_changed.emit(key, value)
    
    def get_language(self) -> str:
        """获取语言设置"""
        return self.get("language", "zh_CN")

    @Slot(str)
    def set_language(self, language: str):
        """设置语言"""
        self.set("language", language)

    def get_theme(self) -> str:
        """获取主题设置"""
        return self.get("theme", "dark")

    @Slot(str)
    def set_theme(self, theme: str):
        """设置主题"""
        self.set("theme", theme)

    @Slot(result='QVariant')
    def get_window_geometry(self):
        """获取窗口几何信息"""
        return self.get("window", {
            "width": 1200,
            "height": 800,
            "x": 100,
            "y": 100
        })

    @Slot(int, int, int, int)
    def set_window_geometry(self, x: int, y: int, width: int, height: int):
        """设置窗口几何信息"""
        self.set("window", {
            "x": x,
            "y": y,
            "width": width,
            "height": height
        })

    def get_game_path(self) -> str:
        """获取游戏路径"""
        return self.get("game.path", "")

    def set_game_path(self, path: str):
        """设置游戏路径"""
        self.set("game.path", path)
