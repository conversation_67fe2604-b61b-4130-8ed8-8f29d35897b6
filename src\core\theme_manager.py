#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主题管理器 - 负责应用程序主题的管理和切换
"""

from PySide6.QtCore import QObject, Signal, Slot, Property


class ThemeManager(QObject):
    """主题管理器"""
    
    # 信号
    theme_changed = Signal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._current_theme = "dark"
        self._themes = {
            "light": {
                "primary": "#2196F3",
                "primaryDark": "#1976D2",
                "accent": "#FF4081",
                "background": "#FAFAFA",
                "surface": "#FFFFFF",
                "error": "#F44336",
                "onPrimary": "#FFFFFF",
                "onSecondary": "#000000",
                "onBackground": "#000000",
                "onSurface": "#000000",
                "onError": "#FFFFFF",
                "textPrimary": "#212121",
                "textSecondary": "#757575",
                "divider": "#BDBDBD",
                "border": "#E0E0E0"
            },
            "dark": {
                "primary": "#BB86FC",
                "primaryDark": "#3700B3",
                "accent": "#03DAC6",
                "background": "#121212",
                "surface": "#1E1E1E",
                "error": "#CF6679",
                "onPrimary": "#000000",
                "onSecondary": "#FFFFFF",
                "onBackground": "#FFFFFF",
                "onSurface": "#FFFFFF",
                "onError": "#000000",
                "textPrimary": "#FFFFFF",
                "textSecondary": "#B3B3B3",
                "divider": "#373737",
                "border": "#373737"
            }
        }
    
    @Property(str, notify=theme_changed)
    def currentTheme(self):
        """当前主题"""
        return self._current_theme
    
    @currentTheme.setter
    def currentTheme(self, theme):
        """设置当前主题"""
        if theme != self._current_theme and theme in self._themes:
            self._current_theme = theme
            self.theme_changed.emit(theme)
    
    def get_color(self, color_name: str) -> str:
        """获取当前主题的颜色"""
        theme_colors = self._themes.get(self._current_theme, self._themes["dark"])
        return theme_colors.get(color_name, "#000000")
    
    def get_theme_colors(self) -> dict:
        """获取当前主题的所有颜色"""
        return self._themes.get(self._current_theme, self._themes["dark"])
    
    @Slot()
    def switch_theme(self):
        """切换主题"""
        new_theme = "light" if self._current_theme == "dark" else "dark"
        self.currentTheme = new_theme

    @Slot(str)
    def set_theme(self, theme: str):
        """设置主题"""
        self.currentTheme = theme
