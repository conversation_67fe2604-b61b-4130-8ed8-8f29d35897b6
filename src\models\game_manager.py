#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏管理器 - 负责游戏路径配置和破解文件管理
"""

import os
import shutil
from pathlib import Path
from PySide6.QtCore import QObject, Signal, Slot, Property


class GameManager(QObject):
    """游戏管理器"""
    
    # 信号
    game_path_changed = Signal(str)
    crack_status_changed = Signal(bool)
    status_message_changed = Signal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._game_path = ""
        self._is_cracked = False
        self._status_message = "未配置游戏路径"
        
        self.tools_dir = Path("TOOLS")
        self.online_fix_dir = self.tools_dir / "OnlineFix"
        self.config_file = self.online_fix_dir / "gconfig.ini"
        
        # 确保目录存在
        self.online_fix_dir.mkdir(parents=True, exist_ok=True)
        
        # 加载配置
        self._load_game_config()
    
    @Property(str, notify=game_path_changed)
    def gamePath(self):
        """游戏路径"""
        return self._game_path
    
    @Property(bool, notify=crack_status_changed)
    def isCracked(self):
        """是否已破解"""
        return self._is_cracked
    
    @Property(str, notify=status_message_changed)
    def statusMessage(self):
        """状态消息"""
        return self._status_message
    
    def _load_game_config(self):
        """加载游戏配置"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    path = f.read().strip().strip('"')
                    if path:
                        self.set_game_path(path)
        except Exception as e:
            print(f"加载游戏配置失败: {e}")
    
    @Slot(str)
    def set_game_path(self, path: str):
        """设置游戏路径"""
        path = path.strip().strip('"')
        
        if self._validate_game_path(path):
            self._game_path = path
            self._save_game_config()
            self._check_crack_status()
            self.game_path_changed.emit(path)
            self._update_status_message("游戏路径配置成功")
        else:
            self._update_status_message("无效的游戏路径")
    
    def _validate_game_path(self, path: str) -> bool:
        """验证游戏路径"""
        if not path:
            return False

        game_file = Path(path)
        if not game_file.exists():
            return False

        # 检查是否是nightreign.exe文件
        if game_file.name.lower() != "nightreign.exe":
            return False

        # 检查文件大小（基本验证）
        if game_file.stat().st_size < 1024:  # 至少1KB
            return False

        return True
    
    def _save_game_config(self):
        """保存游戏配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                f.write(f'"{self._game_path}"\n')
        except Exception as e:
            print(f"保存游戏配置失败: {e}")
    
    def _check_crack_status(self):
        """检查破解状态"""
        if not self._game_path:
            self._is_cracked = False
            self.crack_status_changed.emit(False)
            return
        
        game_dir = Path(self._game_path).parent
        
        # 检查破解文件是否存在
        crack_files = [
            "OnlineFix.ini",
            "OnlineFix64.dll", 
            "dlllist.txt",
            "winmm.dll"
        ]
        
        all_exist = all((game_dir / file).exists() for file in crack_files)
        
        self._is_cracked = all_exist
        self.crack_status_changed.emit(all_exist)
    
    @Slot()
    def apply_crack(self):
        """应用破解"""
        if not self._game_path:
            self._update_status_message("请先配置游戏路径")
            return

        try:
            game_dir = Path(self._game_path).parent

            # 检查破解文件是否存在
            crack_files = [
                "OnlineFix.ini",
                "OnlineFix64.dll",
                "dlllist.txt",
                "winmm.dll"
            ]

            missing_files = []
            for file_name in crack_files:
                source_file = self.online_fix_dir / file_name
                if not source_file.exists():
                    missing_files.append(file_name)

            if missing_files:
                self._update_status_message(f"缺少破解文件: {', '.join(missing_files)}")
                return

            # 复制破解文件
            copied_files = []
            for file_name in crack_files:
                source_file = self.online_fix_dir / file_name
                target_file = game_dir / file_name

                # 备份原文件（如果存在）
                if target_file.exists():
                    backup_file = game_dir / f"{file_name}.backup"
                    shutil.copy2(target_file, backup_file)

                shutil.copy2(source_file, target_file)
                copied_files.append(file_name)

            self._check_crack_status()
            self._update_status_message(f"破解应用成功，已复制 {len(copied_files)} 个文件")

        except Exception as e:
            self._update_status_message(f"破解应用失败: {str(e)}")
    
    @Slot()
    def remove_crack(self):
        """移除破解"""
        if not self._game_path:
            self._update_status_message("请先配置游戏路径")
            return

        try:
            game_dir = Path(self._game_path).parent

            # 删除破解文件
            crack_files = [
                "OnlineFix.ini",
                "OnlineFix64.dll",
                "dlllist.txt",
                "winmm.dll"
            ]

            removed_files = []
            for file_name in crack_files:
                target_file = game_dir / file_name
                if target_file.exists():
                    target_file.unlink()
                    removed_files.append(file_name)

                    # 恢复备份文件（如果存在）
                    backup_file = game_dir / f"{file_name}.backup"
                    if backup_file.exists():
                        shutil.move(backup_file, target_file)

            self._check_crack_status()
            if removed_files:
                self._update_status_message(f"破解移除成功，已删除 {len(removed_files)} 个文件")
            else:
                self._update_status_message("没有找到需要移除的破解文件")

        except Exception as e:
            self._update_status_message(f"破解移除失败: {str(e)}")
    
    def _update_status_message(self, message: str):
        """更新状态消息"""
        self._status_message = message
        self.status_message_changed.emit(message)
    
    def get_game_directory(self) -> str:
        """获取游戏目录"""
        if self._game_path:
            return str(Path(self._game_path).parent)
        return ""

    @Slot(result='QVariantList')
    def get_crack_files_info(self):
        """获取破解文件信息"""
        crack_files = [
            "OnlineFix.ini",
            "OnlineFix64.dll",
            "dlllist.txt",
            "winmm.dll"
        ]

        files_info = []
        for file_name in crack_files:
            source_file = self.online_fix_dir / file_name
            info = {
                "name": file_name,
                "exists": source_file.exists(),
                "size": source_file.stat().st_size if source_file.exists() else 0
            }
            files_info.append(info)

        return files_info

    @Slot(result=bool)
    def validate_crack_files(self):
        """验证破解文件完整性"""
        crack_files = [
            "OnlineFix.ini",
            "OnlineFix64.dll",
            "dlllist.txt",
            "winmm.dll"
        ]

        for file_name in crack_files:
            source_file = self.online_fix_dir / file_name
            if not source_file.exists():
                return False

            # 检查文件大小（基本验证）
            if source_file.stat().st_size == 0:
                return False

        return True
