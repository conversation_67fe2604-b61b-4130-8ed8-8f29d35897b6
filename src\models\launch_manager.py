#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动管理器 - 负责游戏的启动和参数管理
"""

import os
import subprocess
from pathlib import Path
from PySide6.QtCore import QObject, Signal, Slot, Property, QProcess


class LaunchManager(QObject):
    """启动管理器"""
    
    # 信号
    launch_status_changed = Signal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.me3_dir = Path("ME3")
        self.me3_exe = self.me3_dir / "bin" / "me3.exe"
        self._launch_status = "就绪"
        self._process = None
    
    @Property(str, notify=launch_status_changed)
    def launch_status(self) -> str:
        """启动状态"""
        return self._launch_status
    
    def _update_launch_status(self, status: str):
        """更新启动状态"""
        self._launch_status = status
        self.launch_status_changed.emit(status)
    
    @Slot(str, str, bool)
    def launch_game(self, game_path: str, profile_path: str, skip_steam: bool = True):
        """启动游戏"""
        if not game_path:
            self._update_launch_status("游戏路径未配置")
            return False
        
        if not profile_path:
            self._update_launch_status("配置文件未选择")
            return False
        
        if not self.me3_exe.exists():
            self._update_launch_status("ME3工具未安装")
            return False
        
        try:
            # 构建命令行参数
            args = [
                str(self.me3_exe),
                "launch",
                "--exe", game_path
            ]
            
            if skip_steam:
                args.append("--skip-steam-init")
            
            args.extend(["--game", "nightreign", "-p", profile_path])
            
            # 启动进程
            self._process = QProcess()
            self._process.finished.connect(self._on_process_finished)
            self._process.errorOccurred.connect(self._on_process_error)
            
            self._process.start(args[0], args[1:])
            
            self._update_launch_status("游戏启动中...")
            return True
            
        except Exception as e:
            self._update_launch_status(f"启动失败: {str(e)}")
            return False
    
    def _on_process_finished(self, exit_code, exit_status):
        """进程结束回调"""
        if exit_code == 0:
            self._update_launch_status("游戏已退出")
        else:
            self._update_launch_status(f"游戏异常退出，退出码: {exit_code}")
    
    def _on_process_error(self, error):
        """进程错误回调"""
        error_messages = {
            QProcess.FailedToStart: "启动失败",
            QProcess.Crashed: "进程崩溃",
            QProcess.Timedout: "操作超时",
            QProcess.WriteError: "写入错误",
            QProcess.ReadError: "读取错误",
            QProcess.UnknownError: "未知错误"
        }
        
        error_message = error_messages.get(error, "未知错误")
        self._update_launch_status(f"错误: {error_message}")
    
    @Slot(str, str, bool, result=str)
    def generate_command_preview(self, game_path: str, profile_path: str, skip_steam: bool = True) -> str:
        """生成命令预览"""
        if not game_path or not profile_path:
            return "请先配置游戏路径和选择配置文件"
        
        me3_path = "./ME3/bin/me3.exe"
        skip_steam_arg = " --skip-steam-init" if skip_steam else ""
        
        return f'{me3_path} launch --exe "{game_path}"{skip_steam_arg} --game nightreign -p "{profile_path}"'
