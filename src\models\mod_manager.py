#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Mod管理器 - 负责ME3配置文件的生成、编辑和管理
"""

import os
import toml
from pathlib import Path
from PySide6.QtCore import QObject, Signal, Slot, Property, QStringListModel


class ModManager(QObject):
    """Mod管理器"""
    
    # 信号
    current_profile_changed = Signal(str)
    profile_content_changed = Signal(str)
    profiles_list_changed = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.mods_dir = Path("Mods")
        self._current_profile = ""
        self._profile_content = ""
        self._profiles_list = []
        
        # 确保目录存在
        self.mods_dir.mkdir(exist_ok=True)
        
        # 加载配置文件列表
        self._load_profiles_list()
    
    @Property(str, notify=current_profile_changed)
    def currentProfile(self):
        """当前配置文件"""
        return self._current_profile
    
    @Property(str, notify=profile_content_changed)
    def profileContent(self):
        """配置文件内容"""
        return self._profile_content
    
    @Property(list, notify=profiles_list_changed)
    def profilesList(self):
        """配置文件列表"""
        return self._profiles_list
    
    def _load_profiles_list(self):
        """加载配置文件列表"""
        try:
            profiles = []
            for file_path in self.mods_dir.glob("*.me3"):
                profiles.append(file_path.stem)
            
            self._profiles_list = sorted(profiles)
            self.profiles_list_changed.emit()
            
        except Exception as e:
            print(f"加载配置文件列表失败: {e}")
    
    @Slot(str)
    def load_profile(self, profile_name: str):
        """加载配置文件"""
        try:
            profile_file = self.mods_dir / f"{profile_name}.me3"
            
            if profile_file.exists():
                with open(profile_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                self._current_profile = profile_name
                self._profile_content = content
                
                self.current_profile_changed.emit(profile_name)
                self.profile_content_changed.emit(content)
            else:
                print(f"配置文件不存在: {profile_file}")
                
        except Exception as e:
            print(f"加载配置文件失败: {e}")
    
    @Slot(str, str)
    def save_profile(self, profile_name: str, content: str):
        """保存配置文件"""
        try:
            # 验证TOML格式
            toml.loads(content)
            
            profile_file = self.mods_dir / f"{profile_name}.me3"
            
            with open(profile_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            self._current_profile = profile_name
            self._profile_content = content
            
            # 更新列表
            self._load_profiles_list()
            
            self.current_profile_changed.emit(profile_name)
            self.profile_content_changed.emit(content)
            
        except toml.TomlDecodeError as e:
            print(f"TOML格式错误: {e}")
            raise ValueError(f"配置文件格式错误: {e}")
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            raise
    
    @Slot(str)
    def create_new_profile(self, profile_name: str):
        """创建新的配置文件"""
        try:
            # 默认配置模板
            default_config = {
                "profileVersion": "v1",
                "packages": [],
                "natives": []
            }
            
            content = toml.dumps(default_config)
            self.save_profile(profile_name, content)
            
        except Exception as e:
            print(f"创建配置文件失败: {e}")
            raise
    
    @Slot(str)
    def delete_profile(self, profile_name: str):
        """删除配置文件"""
        try:
            profile_file = self.mods_dir / f"{profile_name}.me3"
            
            if profile_file.exists():
                profile_file.unlink()
                
                # 如果删除的是当前配置文件，清空当前状态
                if self._current_profile == profile_name:
                    self._current_profile = ""
                    self._profile_content = ""
                    self.current_profile_changed.emit("")
                    self.profile_content_changed.emit("")
                
                # 更新列表
                self._load_profiles_list()
                
        except Exception as e:
            print(f"删除配置文件失败: {e}")
            raise
    
    @Slot(str, str, str)
    def add_package(self, package_id: str, package_path: str, load_order: str = ""):
        """添加包"""
        try:
            if not self._profile_content:
                return
            
            config = toml.loads(self._profile_content)
            
            # 确保packages数组存在
            if "packages" not in config:
                config["packages"] = []
            
            # 创建新包
            new_package = {
                "id": package_id,
                "path": package_path
            }
            
            # 添加加载顺序（如果指定）
            if load_order:
                if load_order.startswith("after:"):
                    new_package["load_after"] = [load_order[6:]]
                elif load_order.startswith("before:"):
                    new_package["load_before"] = [load_order[7:]]
            
            config["packages"].append(new_package)
            
            # 更新内容
            new_content = toml.dumps(config)
            self._profile_content = new_content
            self.profile_content_changed.emit(new_content)
            
        except Exception as e:
            print(f"添加包失败: {e}")
            raise
    
    @Slot(str)
    def add_native(self, native_path: str):
        """添加原生DLL"""
        try:
            if not self._profile_content:
                return
            
            config = toml.loads(self._profile_content)
            
            # 确保natives数组存在
            if "natives" not in config:
                config["natives"] = []
            
            # 创建新原生模块
            new_native = {
                "path": native_path
            }
            
            config["natives"].append(new_native)
            
            # 更新内容
            new_content = toml.dumps(config)
            self._profile_content = new_content
            self.profile_content_changed.emit(new_content)
            
        except Exception as e:
            print(f"添加原生模块失败: {e}")
            raise
    
    @Slot(str, result=str)
    def get_profile_path(self, profile_name: str) -> str:
        """获取配置文件完整路径"""
        if profile_name:
            return str(self.mods_dir / f"{profile_name}.me3")
        return ""
