#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具管理器 - 负责ME3工具的下载、安装和版本管理
"""

import os
import json
import zipfile
import requests
from pathlib import Path
from PySide6.QtCore import QObject, Signal, Slot, Property, QThread, QTimer


class DownloadWorker(QThread):
    """下载工作线程"""
    
    progress_updated = Signal(int)  # 下载进度
    download_finished = Signal(bool, str)  # 下载完成，成功标志，消息
    
    def __init__(self, url, file_path, parent=None):
        super().__init__(parent)
        self.url = url
        self.file_path = file_path
        self.is_cancelled = False
    
    def run(self):
        """执行下载"""
        try:
            response = requests.get(self.url, stream=True)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            downloaded_size = 0
            
            with open(self.file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if self.is_cancelled:
                        break
                    
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)
                        
                        if total_size > 0:
                            progress = int((downloaded_size / total_size) * 100)
                            self.progress_updated.emit(progress)
            
            if not self.is_cancelled:
                self.download_finished.emit(True, "下载完成")
            else:
                # 删除未完成的文件
                if os.path.exists(self.file_path):
                    os.remove(self.file_path)
                self.download_finished.emit(False, "下载已取消")
                
        except Exception as e:
            self.download_finished.emit(False, f"下载失败: {str(e)}")
    
    def cancel(self):
        """取消下载"""
        self.is_cancelled = True


class ToolManager(QObject):
    """工具管理器"""
    
    # 信号
    download_progress_changed = Signal(int)
    download_status_changed = Signal(str)
    tool_version_changed = Signal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.tools_dir = Path("TOOLS")
        self.me3_dir = Path("ME3")
        self.download_worker = None
        self._current_version = ""
        self._download_progress = 0
        self._download_status = "就绪"
        
        # GitHub加速代理列表
        self.proxy_urls = [
            "https://gh-proxy.com/",
            "https://ghproxy.net/",
            "https://ghfast.top/"
        ]
        self.current_proxy_index = 0
        
        # 确保目录存在
        self.tools_dir.mkdir(exist_ok=True)
        self.me3_dir.mkdir(exist_ok=True)
        
        # 检查当前版本
        self._check_current_version()
    
    @Property(str, notify=tool_version_changed)
    def currentVersion(self):
        """当前工具版本"""
        return self._current_version
    
    @Property(int, notify=download_progress_changed)
    def downloadProgress(self):
        """下载进度"""
        return self._download_progress
    
    @Property(str, notify=download_status_changed)
    def downloadStatus(self):
        """下载状态"""
        return self._download_status
    
    def _check_current_version(self):
        """检查当前安装的版本"""
        version_file = self.me3_dir / "version.json"
        if version_file.exists():
            try:
                with open(version_file, 'r', encoding='utf-8') as f:
                    version_data = json.load(f)
                    self._current_version = version_data.get("version", "")
                    self.tool_version_changed.emit(self._current_version)
            except Exception as e:
                print(f"读取版本信息失败: {e}")
    
    @Slot()
    def check_latest_version(self):
        """检查最新版本"""
        try:
            self._update_download_status("正在检查最新版本...")

            # 使用GitHub API获取最新版本信息
            api_url = "https://api.github.com/repos/garyttierney/me3/releases/latest"
            response = requests.get(api_url, timeout=10)
            response.raise_for_status()

            release_data = response.json()
            latest_version = release_data.get("tag_name", "unknown")

            self._update_download_status(f"最新版本: {latest_version}")

            # 检查是否需要更新
            if latest_version != self._current_version:
                self._update_download_status(f"发现新版本: {latest_version}")
            else:
                self._update_download_status("已是最新版本")

        except Exception as e:
            self._update_download_status(f"检查版本失败: {str(e)}")
    
    @Slot()
    def download_latest_tool(self):
        """下载最新工具"""
        if self.download_worker and self.download_worker.isRunning():
            self._update_download_status("正在下载中...")
            return
        
        # 构建下载URL
        base_url = "https://github.com/garyttierney/me3/releases/latest/download/me3-windows-amd64.zip"
        proxy_url = self.proxy_urls[self.current_proxy_index]
        download_url = proxy_url + base_url
        
        # 下载文件路径
        download_file = self.tools_dir / "me3-windows-amd64.zip"
        
        # 创建下载工作线程
        self.download_worker = DownloadWorker(download_url, str(download_file))
        self.download_worker.progress_updated.connect(self._on_download_progress)
        self.download_worker.download_finished.connect(self._on_download_finished)
        
        self._update_download_status("开始下载...")
        self.download_worker.start()
    
    def _on_download_progress(self, progress):
        """下载进度更新"""
        self._download_progress = progress
        self.download_progress_changed.emit(progress)
        self._update_download_status(f"下载中... {progress}%")
    
    def _on_download_finished(self, success, message):
        """下载完成"""
        self._update_download_status(message)
        
        if success:
            # 解压文件
            self._extract_tool()
    
    def _extract_tool(self):
        """解压工具"""
        try:
            zip_file = self.tools_dir / "me3-windows-amd64.zip"
            
            with zipfile.ZipFile(zip_file, 'r') as zip_ref:
                zip_ref.extractall(self.me3_dir)
            
            # 保存版本信息
            self._save_version_info()
            
            self._update_download_status("安装完成")
            
        except Exception as e:
            self._update_download_status(f"解压失败: {str(e)}")
    
    def _save_version_info(self):
        """保存版本信息"""
        # 这里应该从GitHub API获取实际版本号
        # 暂时使用当前时间作为版本标识
        import datetime
        version_info = {
            "version": datetime.datetime.now().strftime("%Y%m%d_%H%M%S"),
            "download_time": datetime.datetime.now().isoformat()
        }
        
        version_file = self.me3_dir / "version.json"
        with open(version_file, 'w', encoding='utf-8') as f:
            json.dump(version_info, f, indent=2, ensure_ascii=False)
        
        self._current_version = version_info["version"]
        self.tool_version_changed.emit(self._current_version)
    
    def _update_download_status(self, status):
        """更新下载状态"""
        self._download_status = status
        self.download_status_changed.emit(status)
    
    @Slot()
    def switch_proxy(self):
        """切换代理"""
        self.current_proxy_index = (self.current_proxy_index + 1) % len(self.proxy_urls)
        current_proxy = self.proxy_urls[self.current_proxy_index]
        self._update_download_status(f"已切换到代理: {current_proxy}")
    
    @Slot()
    def cancel_download(self):
        """取消下载"""
        if self.download_worker and self.download_worker.isRunning():
            self.download_worker.cancel()
            self._update_download_status("正在取消下载...")
