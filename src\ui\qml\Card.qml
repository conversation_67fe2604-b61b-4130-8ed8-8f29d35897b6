import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Controls.Material 2.15

Rectangle {
    id: card
    
    color: Material.dialogColor
    radius: 8
    border.width: 1
    border.color: Material.dividerColor
    
    // 阴影效果 (暂时注释掉)
    /*
    layer.enabled: true
    layer.effect: DropShadow {
        radius: 4
        samples: 9
        color: "#20000000"
        verticalOffset: 2
    }
    */
}
