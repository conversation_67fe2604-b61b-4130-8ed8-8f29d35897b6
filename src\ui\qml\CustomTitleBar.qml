import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Controls.Material 2.15
import QtQuick.Layouts 1.15

Rectangle {
    id: titleBar
    
    color: Material.primary
    
    // 信号
    signal closeClicked()
    signal minimizeClicked()
    signal maximizeClicked()
    signal startDrag(point mousePos)
    signal drag(point mousePos)
    signal endDrag()
    
    RowLayout {
        anchors.fill: parent
        anchors.leftMargin: 10
        anchors.rightMargin: 5
        spacing: 10
        
        // 应用图标和标题
        RowLayout {
            Layout.fillWidth: true
            spacing: 10
            
            Rectangle {
                width: 24
                height: 24
                color: Material.accent
                radius: 4

                Text {
                    anchors.centerIn: parent
                    text: "M"
                    color: Material.primaryTextColor
                    font.pixelSize: 16
                    font.bold: true
                }
            }
            
            Text {
                text: qsTr("ME3 Mod Manager")
                color: Material.primaryTextColor
                font.pixelSize: 14
                font.bold: true
            }
            
            // 拖动区域
            MouseArea {
                Layout.fillWidth: true
                Layout.fillHeight: true

                onPressed: function(mouse) {
                    titleBar.startDrag(Qt.point(mouse.x, mouse.y))
                }
                onPositionChanged: function(mouse) {
                    titleBar.drag(Qt.point(mouse.x, mouse.y))
                }
                onReleased: titleBar.endDrag()
                onDoubleClicked: titleBar.maximizeClicked()
            }
        }
        
        // 主题切换按钮
        ToolButton {
            text: themeManager.currentTheme === "dark" ? "☀" : "🌙"

            onClicked: themeManager.switch_theme()

            ToolTip.visible: hovered
            ToolTip.text: qsTr("切换主题")
        }

        // 语言切换按钮
        ToolButton {
            text: "🌐"

            onClicked: languageMenu.open()

            ToolTip.visible: hovered
            ToolTip.text: qsTr("切换语言")

            Menu {
                id: languageMenu

                MenuItem {
                    text: "中文"
                    onTriggered: configManager.set_language("zh_CN")
                }

                MenuItem {
                    text: "English"
                    onTriggered: configManager.set_language("en_US")
                }
            }
        }
        
        // 窗口控制按钮
        RowLayout {
            spacing: 0
            
            // 最小化按钮
            ToolButton {
                text: "−"

                onClicked: titleBar.minimizeClicked()

                ToolTip.visible: hovered
                ToolTip.text: qsTr("最小化")
            }

            // 最大化/还原按钮
            ToolButton {
                text: mainWindow.visibility === Window.Maximized ? "❐" : "□"

                onClicked: titleBar.maximizeClicked()

                ToolTip.visible: hovered
                ToolTip.text: mainWindow.visibility === Window.Maximized ?
                            qsTr("还原") : qsTr("最大化")
            }

            // 关闭按钮
            ToolButton {
                text: "×"

                onClicked: titleBar.closeClicked()

                ToolTip.visible: hovered
                ToolTip.text: qsTr("关闭")

                // 悬停效果
                background: Rectangle {
                    color: parent.hovered ? "#e81123" : "transparent"
                    radius: 2
                }
            }
        }
    }
}
