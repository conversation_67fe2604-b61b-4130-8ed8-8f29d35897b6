import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Controls.Material 2.15
import QtQuick.Layouts 1.15
import QtQuick.Dialogs

Page {
    id: gamePage
    objectName: "gamePage"
    
    title: qsTr("基础配置")
    
    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 20
        spacing: 20
        
        // 页面标题
        Text {
            text: qsTr("游戏配置与破解管理")
            font.pixelSize: 24
            font.bold: true
            color: Material.primaryTextColor
        }
        
        // 游戏路径配置卡片
        Card {
            Layout.fillWidth: true
            Layout.preferredHeight: 200
            
            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 20
                spacing: 15
                
                Text {
                    text: qsTr("游戏路径配置")
                    font.pixelSize: 18
                    font.bold: true
                    color: Material.primaryTextColor
                }
                
                RowLayout {
                    Text {
                        text: qsTr("当前路径:")
                        color: Material.secondaryTextColor
                    }
                    
                    Text {
                        text: gameManager.gamePath || qsTr("未配置")
                        color: Material.primaryTextColor
                        Layout.fillWidth: true
                        elide: Text.ElideMiddle
                    }
                }
                
                RowLayout {
                    Text {
                        text: qsTr("破解状态:")
                        color: Material.secondaryTextColor
                    }
                    
                    Text {
                        text: gameManager.isCracked ? qsTr("已破解") : qsTr("未破解")
                        color: gameManager.isCracked ? Material.accent : Material.color(Material.Orange)
                        font.bold: true
                    }
                }
                
                Text {
                    text: gameManager.statusMessage
                    color: Material.secondaryTextColor
                    Layout.fillWidth: true
                    wrapMode: Text.WordWrap
                }
            }
        }
        
        // 操作按钮
        Card {
            Layout.fillWidth: true
            
            RowLayout {
                anchors.fill: parent
                anchors.margins: 20
                spacing: 15
                
                Button {
                    text: qsTr("选择游戏文件")
                    Material.background: Material.primary
                    
                    onClicked: fileDialog.open()
                }
                
                Button {
                    text: gameManager.isCracked ? qsTr("移除破解") : qsTr("应用破解")
                    enabled: gameManager.gamePath !== ""
                    
                    onClicked: {
                        if (gameManager.isCracked) {
                            gameManager.remove_crack()
                        } else {
                            gameManager.apply_crack()
                        }
                    }
                }
                
                Item {
                    Layout.fillWidth: true
                }
            }
        }
        
        // 破解文件信息
        Card {
            Layout.fillWidth: true

            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 20
                spacing: 10

                RowLayout {
                    Text {
                        text: qsTr("破解文件状态")
                        font.pixelSize: 16
                        font.bold: true
                        color: Material.primaryTextColor
                    }

                    Item {
                        Layout.fillWidth: true
                    }

                    Text {
                        text: gameManager.validate_crack_files() ? qsTr("完整") : qsTr("不完整")
                        color: gameManager.validate_crack_files() ? Material.accent : Material.color(Material.Orange)
                        font.bold: true
                    }
                }

                Repeater {
                    model: gameManager.get_crack_files_info()

                    RowLayout {
                        Text {
                            text: "• " + modelData.name
                            color: Material.secondaryTextColor
                            Layout.preferredWidth: 150
                        }

                        Text {
                            text: modelData.exists ? qsTr("存在") : qsTr("缺失")
                            color: modelData.exists ? Material.accent : Material.color(Material.Red)
                            Layout.preferredWidth: 60
                        }

                        Text {
                            text: modelData.exists ? (Math.round(modelData.size / 1024) + " KB") : ""
                            color: Material.secondaryTextColor
                            Layout.fillWidth: true
                        }
                    }
                }
            }
        }

        // 说明信息
        Card {
            Layout.fillWidth: true

            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 20
                spacing: 10

                Text {
                    text: qsTr("使用说明")
                    font.pixelSize: 16
                    font.bold: true
                    color: Material.primaryTextColor
                }

                Text {
                    text: qsTr("• 请选择nightreign.exe游戏文件")
                    color: Material.secondaryTextColor
                    wrapMode: Text.WordWrap
                    Layout.fillWidth: true
                }

                Text {
                    text: qsTr("• 破解文件将复制到游戏根目录，原文件会自动备份")
                    color: Material.secondaryTextColor
                    wrapMode: Text.WordWrap
                    Layout.fillWidth: true
                }

                Text {
                    text: qsTr("• 移除破解将删除相关文件并恢复备份")
                    color: Material.secondaryTextColor
                    wrapMode: Text.WordWrap
                    Layout.fillWidth: true
                }
            }
        }
        
        // 填充空间
        Item {
            Layout.fillHeight: true
        }
    }
    
    // 文件选择对话框
    FileDialog {
        id: fileDialog
        title: qsTr("选择游戏文件")
        nameFilters: ["Executable files (*.exe)"]
        
        onAccepted: {
            var path = fileDialog.selectedFile.toString()
            // 移除file://前缀
            if (path.startsWith("file:///")) {
                path = path.substring(8)
            }
            gameManager.set_game_path(path)
        }
    }
}
