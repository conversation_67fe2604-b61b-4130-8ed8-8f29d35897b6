import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Controls.Material 2.15
import QtQuick.Layouts 1.15

Page {
    id: launchPage
    objectName: "launchPage"
    
    title: qsTr("启动游戏")
    
    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 20
        spacing: 20
        
        // 页面标题
        Text {
            text: qsTr("游戏启动")
            font.pixelSize: 24
            font.bold: true
            color: Material.primaryTextColor
        }
        
        // 启动配置
        Card {
            Layout.fillWidth: true
            Layout.preferredHeight: 200
            
            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 20
                spacing: 15
                
                Text {
                    text: qsTr("启动配置")
                    font.pixelSize: 18
                    font.bold: true
                    color: Material.primaryTextColor
                }
                
                RowLayout {
                    Text {
                        text: qsTr("游戏路径:")
                        color: Material.secondaryTextColor
                    }
                    
                    Text {
                        text: gameManager.gamePath || qsTr("未配置")
                        color: Material.primaryTextColor
                        Layout.fillWidth: true
                        elide: Text.ElideMiddle
                    }
                }
                
                RowLayout {
                    Text {
                        text: qsTr("配置文件:")
                        color: Material.secondaryTextColor
                    }
                    
                    ComboBox {
                        id: profileComboBox
                        Layout.fillWidth: true
                        model: modManager.profilesList
                        currentIndex: {
                            var profiles = modManager.profilesList
                            var current = modManager.currentProfile
                            return profiles.indexOf(current)
                        }
                    }
                }
                
                RowLayout {
                    CheckBox {
                        id: skipSteamCheckBox
                        text: qsTr("跳过Steam初始化")
                        checked: true
                    }
                    
                    Item {
                        Layout.fillWidth: true
                    }
                }
            }
        }
        
        // 启动按钮
        Card {
            Layout.fillWidth: true
            
            RowLayout {
                anchors.fill: parent
                anchors.margins: 20
                spacing: 15
                
                Button {
                    text: qsTr("启动游戏")
                    Material.background: Material.primary
                    enabled: gameManager.gamePath !== "" && profileComboBox.currentText !== ""

                    onClicked: {
                        var profilePath = modManager.get_profile_path(profileComboBox.currentText)
                        launchManager.launch_game(gameManager.gamePath, profilePath, skipSteamCheckBox.checked)
                    }
                }

                Item {
                    Layout.fillWidth: true
                }

                Text {
                    id: statusText
                    text: launchManager.launch_status
                    color: Material.secondaryTextColor
                }
            }
        }
        
        // 启动命令预览
        Card {
            Layout.fillWidth: true
            
            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 20
                spacing: 10
                
                Text {
                    text: qsTr("启动命令预览")
                    font.pixelSize: 16
                    font.bold: true
                    color: Material.primaryTextColor
                }
                
                ScrollView {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 100
                    
                    TextArea {
                        id: commandPreview
                        text: generateCommandPreview()
                        readOnly: true
                        selectByMouse: true
                        wrapMode: TextArea.Wrap
                        font.family: "Consolas, Monaco, monospace"
                        
                        background: Rectangle {
                            color: Material.dialogColor
                            border.color: Material.dividerColor
                            border.width: 1
                            radius: 4
                        }
                    }
                }
            }
        }
        
        // 填充空间
        Item {
            Layout.fillHeight: true
        }
    }
    
    function generateCommandPreview() {
        if (!gameManager.gamePath || !profileComboBox.currentText) {
            return qsTr("请先配置游戏路径和选择配置文件")
        }

        var profilePath = modManager.get_profile_path(profileComboBox.currentText)
        return launchManager.generate_command_preview(gameManager.gamePath, profilePath, skipSteamCheckBox.checked)
    }
    
    // 监听变化以更新命令预览
    Connections {
        target: gameManager
        function onGamePathChanged() {
            commandPreview.text = generateCommandPreview()
        }
    }
    
    Connections {
        target: modManager
        function onCurrentProfileChanged() {
            commandPreview.text = generateCommandPreview()
        }
    }
    
    onVisibleChanged: {
        if (visible) {
            commandPreview.text = generateCommandPreview()
        }
    }
}
