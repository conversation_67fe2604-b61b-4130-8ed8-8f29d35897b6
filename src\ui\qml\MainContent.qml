import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Controls.Material 2.15
import QtQuick.Layouts 1.15

Rectangle {
    id: mainContent
    
    color: Material.backgroundColor
    
    RowLayout {
        anchors.fill: parent
        spacing: 0
        
        // 侧边导航栏
        Rectangle {
            Layout.preferredWidth: 200
            Layout.fillHeight: true
            color: Material.dialogColor
            
            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 10
                spacing: 5
                
                // 导航按钮
                NavigationButton {
                    text: qsTr("工具管理")
                    iconText: "🔧"
                    selected: stackView.currentItem && stackView.currentItem.objectName === "toolsPage"
                    onClicked: stackView.replace("ToolsPage.qml")
                }

                NavigationButton {
                    text: qsTr("基础配置")
                    iconText: "⚙"
                    selected: stackView.currentItem && stackView.currentItem.objectName === "gamePage"
                    onClicked: stackView.replace("GamePage.qml")
                }

                NavigationButton {
                    text: qsTr("Mod配置")
                    iconText: "📦"
                    selected: stackView.currentItem && stackView.currentItem.objectName === "modsPage"
                    onClicked: stackView.replace("ModsPage.qml")
                }

                NavigationButton {
                    text: qsTr("启动游戏")
                    iconText: "🚀"
                    selected: stackView.currentItem && stackView.currentItem.objectName === "launchPage"
                    onClicked: stackView.replace("LaunchPage.qml")
                }

                // 填充空间
                Item {
                    Layout.fillHeight: true
                }

                // 关于按钮
                NavigationButton {
                    text: qsTr("关于")
                    iconText: "ℹ"
                    onClicked: aboutDialog.open()
                }
            }
        }
        
        // 分隔线
        Rectangle {
            Layout.preferredWidth: 1
            Layout.fillHeight: true
            color: Material.dividerColor
        }
        
        // 主内容区域
        StackView {
            id: stackView
            Layout.fillWidth: true
            Layout.fillHeight: true
            
            initialItem: "ToolsPage.qml"
            
            // 页面切换动画
            pushEnter: Transition {
                PropertyAnimation {
                    property: "opacity"
                    from: 0
                    to: 1
                    duration: 200
                }
            }
            
            pushExit: Transition {
                PropertyAnimation {
                    property: "opacity"
                    from: 1
                    to: 0
                    duration: 200
                }
            }
            
            replaceEnter: Transition {
                PropertyAnimation {
                    property: "opacity"
                    from: 0
                    to: 1
                    duration: 200
                }
            }
            
            replaceExit: Transition {
                PropertyAnimation {
                    property: "opacity"
                    from: 1
                    to: 0
                    duration: 200
                }
            }
        }
    }
    
    // 关于对话框
    Dialog {
        id: aboutDialog
        
        title: qsTr("关于 ME3 Mod Manager")
        modal: true
        anchors.centerIn: parent
        
        ColumnLayout {
            spacing: 10
            
            Text {
                text: qsTr("ME3 Mod Manager v1.0.0")
                font.pixelSize: 16
                font.bold: true
                color: Material.primaryTextColor
            }
            
            Text {
                text: qsTr("基于PySide6+QML的现代化mod管理器")
                color: Material.secondaryTextColor
            }
            
            Text {
                text: qsTr("支持ME3工具的自动下载和mod配置管理")
                color: Material.secondaryTextColor
            }
        }
        
        standardButtons: Dialog.Ok
    }
}
