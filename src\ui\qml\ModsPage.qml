import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Controls.Material 2.15
import QtQuick.Layouts 1.15

Page {
    id: modsPage
    objectName: "modsPage"
    
    title: qsTr("Mod配置")
    
    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 20
        spacing: 20
        
        // 页面标题
        Text {
            text: qsTr("Mod配置管理")
            font.pixelSize: 24
            font.bold: true
            color: Material.primaryTextColor
        }
        
        // 配置文件管理
        Card {
            Layout.fillWidth: true
            Layout.preferredHeight: 150
            
            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 20
                spacing: 15
                
                RowLayout {
                    Text {
                        text: qsTr("配置文件管理")
                        font.pixelSize: 18
                        font.bold: true
                        color: Material.primaryTextColor
                    }
                    
                    Item {
                        Layout.fillWidth: true
                    }
                    
                    Button {
                        text: qsTr("新建配置")
                        onClicked: newProfileDialog.open()
                    }
                }
                
                RowLayout {
                    Text {
                        text: qsTr("当前配置:")
                        color: Material.secondaryTextColor
                    }
                    
                    ComboBox {
                        id: profileComboBox
                        Layout.fillWidth: true
                        model: modManager.profilesList
                        currentIndex: {
                            var profiles = modManager.profilesList
                            var current = modManager.currentProfile
                            return profiles.indexOf(current)
                        }
                        
                        onCurrentTextChanged: {
                            if (currentText && currentText !== modManager.currentProfile) {
                                modManager.load_profile(currentText)
                            }
                        }
                    }
                    
                    Button {
                        text: qsTr("删除")
                        enabled: profileComboBox.currentText !== ""
                        onClicked: deleteConfirmDialog.open()
                    }
                }
            }
        }
        
        // 配置文件编辑器
        Card {
            Layout.fillWidth: true
            Layout.fillHeight: true
            
            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 20
                spacing: 15
                
                RowLayout {
                    Text {
                        text: qsTr("配置文件内容")
                        font.pixelSize: 18
                        font.bold: true
                        color: Material.primaryTextColor
                    }
                    
                    Item {
                        Layout.fillWidth: true
                    }
                    
                    Button {
                        text: qsTr("保存")
                        enabled: profileComboBox.currentText !== ""
                        onClicked: {
                            try {
                                modManager.save_profile(profileComboBox.currentText, textArea.text)
                                statusText.text = qsTr("保存成功")
                                statusText.color = Material.accent
                            } catch (error) {
                                statusText.text = qsTr("保存失败: ") + error.message
                                statusText.color = Material.color(Material.Red)
                            }
                        }
                    }
                }
                
                ScrollView {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    
                    TextArea {
                        id: textArea
                        text: modManager.profileContent
                        selectByMouse: true
                        wrapMode: TextArea.Wrap
                        font.family: "Consolas, Monaco, monospace"
                        
                        background: Rectangle {
                            color: Material.dialogColor
                            border.color: Material.dividerColor
                            border.width: 1
                            radius: 4
                        }
                    }
                }
                
                Text {
                    id: statusText
                    text: ""
                    color: Material.secondaryTextColor
                }
            }
        }
    }
    
    // 新建配置对话框
    Dialog {
        id: newProfileDialog
        title: qsTr("新建配置文件")
        modal: true
        anchors.centerIn: parent
        
        ColumnLayout {
            spacing: 10
            
            Text {
                text: qsTr("配置文件名称:")
                color: Material.primaryTextColor
            }
            
            TextField {
                id: profileNameField
                Layout.fillWidth: true
                placeholderText: qsTr("输入配置文件名称")
            }
        }
        
        standardButtons: Dialog.Ok | Dialog.Cancel
        
        onAccepted: {
            if (profileNameField.text.trim() !== "") {
                try {
                    modManager.create_new_profile(profileNameField.text.trim())
                    profileNameField.text = ""
                } catch (error) {
                    statusText.text = qsTr("创建失败: ") + error.message
                    statusText.color = Material.color(Material.Red)
                }
            }
        }
        
        onRejected: {
            profileNameField.text = ""
        }
    }
    
    // 删除确认对话框
    Dialog {
        id: deleteConfirmDialog
        title: qsTr("确认删除")
        modal: true
        anchors.centerIn: parent
        
        Text {
            text: qsTr("确定要删除配置文件 \"") + profileComboBox.currentText + qsTr("\" 吗？")
            color: Material.primaryTextColor
        }
        
        standardButtons: Dialog.Yes | Dialog.No
        
        onAccepted: {
            try {
                modManager.delete_profile(profileComboBox.currentText)
            } catch (error) {
                statusText.text = qsTr("删除失败: ") + error.message
                statusText.color = Material.color(Material.Red)
            }
        }
    }
}
