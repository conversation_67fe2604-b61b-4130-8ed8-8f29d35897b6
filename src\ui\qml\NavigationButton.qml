import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Controls.Material 2.15
import QtQuick.Layouts 1.15

Button {
    id: navButton

    property string iconText: ""
    property bool selected: false

    Layout.fillWidth: true
    Layout.preferredHeight: 48

    flat: true
    
    background: Rectangle {
        color: navButton.selected ? Material.accent : 
               (navButton.hovered ? Material.listHighlightColor : "transparent")
        radius: 4
        
        Behavior on color {
            ColorAnimation { duration: 150 }
        }
    }
    
    contentItem: RowLayout {
        spacing: 12
        
        Text {
            text: navButton.iconText
            font.pixelSize: 18
            color: navButton.selected ? Material.primaryTextColor : Material.secondaryTextColor

            Behavior on color {
                ColorAnimation { duration: 150 }
            }
        }
        
        Text {
            text: navButton.text
            color: navButton.selected ? Material.primaryTextColor : Material.secondaryTextColor
            font.pixelSize: 14
            Layout.fillWidth: true
            
            Behavior on color {
                ColorAnimation { duration: 150 }
            }
        }
    }
}
