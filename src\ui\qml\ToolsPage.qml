import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Controls.Material 2.15
import QtQuick.Layouts 1.15

Page {
    id: toolsPage
    objectName: "toolsPage"
    
    title: qsTr("工具管理")
    
    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 20
        spacing: 20
        
        // 页面标题
        Text {
            text: qsTr("ME3工具管理")
            font.pixelSize: 24
            font.bold: true
            color: Material.primaryTextColor
        }
        
        // 工具信息卡片
        Card {
            Layout.fillWidth: true
            Layout.preferredHeight: 200
            
            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 20
                spacing: 15
                
                Text {
                    text: qsTr("工具状态")
                    font.pixelSize: 18
                    font.bold: true
                    color: Material.primaryTextColor
                }
                
                RowLayout {
                    Text {
                        text: qsTr("当前版本:")
                        color: Material.secondaryTextColor
                    }
                    
                    Text {
                        text: toolManager.currentVersion || qsTr("未安装")
                        color: Material.primaryTextColor
                        font.bold: true
                    }
                }
                
                RowLayout {
                    Text {
                        text: qsTr("下载状态:")
                        color: Material.secondaryTextColor
                    }
                    
                    Text {
                        text: toolManager.downloadStatus
                        color: Material.primaryTextColor
                    }
                }
                
                // 下载进度条
                ProgressBar {
                    Layout.fillWidth: true
                    value: toolManager.downloadProgress / 100.0
                    visible: toolManager.downloadProgress > 0 && toolManager.downloadProgress < 100
                }
            }
        }
        
        // 操作按钮
        Card {
            Layout.fillWidth: true
            
            RowLayout {
                anchors.fill: parent
                anchors.margins: 20
                spacing: 15
                
                Button {
                    text: qsTr("下载最新版本")
                    Material.background: Material.primary
                    enabled: toolManager.downloadStatus === "就绪" || toolManager.downloadStatus.includes("完成")
                    
                    onClicked: toolManager.download_latest_tool()
                }
                
                Button {
                    text: qsTr("切换代理")
                    flat: true
                    
                    onClicked: toolManager.switch_proxy()
                }
                
                Button {
                    text: qsTr("取消下载")
                    flat: true
                    enabled: toolManager.downloadStatus.includes("下载")
                    
                    onClicked: toolManager.cancel_download()
                }
                
                Item {
                    Layout.fillWidth: true
                }
                
                Button {
                    text: qsTr("检查更新")
                    flat: true
                    
                    onClicked: toolManager.check_latest_version()
                }
            }
        }
        
        // 说明信息
        Card {
            Layout.fillWidth: true
            
            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 20
                spacing: 10
                
                Text {
                    text: qsTr("说明")
                    font.pixelSize: 16
                    font.bold: true
                    color: Material.primaryTextColor
                }
                
                Text {
                    text: qsTr("• ME3工具将自动从GitHub下载最新版本")
                    color: Material.secondaryTextColor
                    wrapMode: Text.WordWrap
                    Layout.fillWidth: true
                }
                
                Text {
                    text: qsTr("• 支持多个加速代理，如遇下载问题可切换代理")
                    color: Material.secondaryTextColor
                    wrapMode: Text.WordWrap
                    Layout.fillWidth: true
                }
                
                Text {
                    text: qsTr("• 工具将解压到ME3文件夹，原压缩包保留在TOOLS文件夹")
                    color: Material.secondaryTextColor
                    wrapMode: Text.WordWrap
                    Layout.fillWidth: true
                }
            }
        }
        
        // 填充空间
        Item {
            Layout.fillHeight: true
        }
    }
}
