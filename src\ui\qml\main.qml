import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Controls.Material 2.15
import QtQuick.Layouts 1.15
import QtQuick.Window 2.15

ApplicationWindow {
    id: mainWindow
    
    // 窗口属性
    width: 1200
    height: 800
    minimumWidth: 800
    minimumHeight: 600
    visible: true
    title: qsTr("ME3 Mod Manager")
    
    // 无边框窗口
    flags: Qt.Window | Qt.FramelessWindowHint
    
    // Material主题设置
    Material.theme: themeManager.currentTheme === "dark" ? Material.Dark : Material.Light
    Material.primary: Material.Blue
    Material.accent: Material.Cyan
    
    // 窗口拖动相关
    property bool windowMoving: false
    property point lastMousePos: Qt.point(0, 0)
    
    // 主布局
    ColumnLayout {
        anchors.fill: parent
        spacing: 0
        
        // 自定义标题栏
        CustomTitleBar {
            id: titleBar
            Layout.fillWidth: true
            Layout.preferredHeight: 40
            
            onCloseClicked: mainWindow.close()
            onMinimizeClicked: mainWindow.showMinimized()
            onMaximizeClicked: {
                if (mainWindow.visibility === Window.Maximized) {
                    mainWindow.showNormal()
                } else {
                    mainWindow.showMaximized()
                }
            }
            
            // 窗口拖动
            onStartDrag: function(mousePos) {
                windowMoving = true
                lastMousePos = mousePos
            }
            
            onDrag: function(mousePos) {
                if (windowMoving) {
                    var delta = Qt.point(mousePos.x - lastMousePos.x, mousePos.y - lastMousePos.y)
                    mainWindow.x += delta.x
                    mainWindow.y += delta.y
                }
            }
            
            onEndDrag: {
                windowMoving = false
            }
        }
        
        // 主内容区域
        Rectangle {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: Material.backgroundColor
            
            // 主要内容
            MainContent {
                anchors.fill: parent
                anchors.margins: 1
            }
        }
    }
    
    // 窗口阴影效果 (暂时注释掉，需要Qt5Compat模块)
    /*
    DropShadow {
        anchors.fill: parent
        source: parent
        radius: 10
        samples: 21
        color: "#40000000"
        visible: mainWindow.visibility !== Window.Maximized
    }
    */
    
    // 连接信号
    Component.onCompleted: {
        // 加载窗口几何信息
        var geometry = configManager.get_window_geometry()
        if (geometry) {
            mainWindow.x = geometry.x || 100
            mainWindow.y = geometry.y || 100
            mainWindow.width = geometry.width || 1200
            mainWindow.height = geometry.height || 800
        }
    }

    onClosing: {
        // 保存窗口几何信息
        configManager.set_window_geometry(mainWindow.x, mainWindow.y, mainWindow.width, mainWindow.height)
    }
}
